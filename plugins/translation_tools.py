# plugins/translation_tools.py
from pyrogram import Client, filters
from langdetect import detect
from googletrans import Translator
import asyncio
from google import genai
from helpers import db, gemini_cli, OpenRouterClient  # Assuming googe_cli is your Gemini client from helpers
from pdb import set_trace
from google.genai import types
# plugins/translation_tools.py

# Initialize translator with explicit version compatibility
translator = Translator()

async def ensure_language(text, user_id, db, default_lang='fa'):
    """
    Check if text is in the target language (default: Persian); if not, translate it.
    Uses user's preferred model and language from DB, with langdetect for detection.
    """
    try:
        # Fetch user preferences from DB
        user_prefs = db.translation_prefs.find_one({'user_id': user_id}) or {}
        target_lang = user_prefs.get('target_lang', default_lang)
        translate_model = user_prefs.get('translate_model', 'googletrans')
        
        # Detect language with langdetect
        detected_lang = detect(text)
        if detected_lang == target_lang:
            return text
        
        SYSTEM_PROMPT = f"Act as an expert translator fluent in both English and Persian. Translate the following text into Persian, ensuring that you preserve the original meaning, tone, and cultural nuances. Adapt idiomatic expressions as needed to create a natural, contextually accurate translation. Text: {text}"
        # Translate based on preferred model
        if translate_model == 'googletrans':
            # Run synchronous googletrans in executor
            translated = await translator.translate(text, dest=target_lang)
            return translated.text

        elif translate_model == 'gemini':
            # Assuming gemini_cli is async-compatible
            cur = db.providers.find_one({'_id': 'google'})
            model = cur['default_model']
            response = gemini_cli.models.generate_content(
                model=model,
                config=types.GenerateContentConfig(
                    system_instruction=SYSTEM_PROMPT),
                contents=[text.strip()]
            )

            return response.text
        elif translate_model == 'openrouter':
            openrouter_client = OpenRouterClient()
            response = openrouter_client.chat_completions_create(
                model=model, 
                messages=[
                    {'role': 'system', 'content': SYSTEM_PROMPT},
                    {'role': 'user', 'content': text.strip()}
                ])
            response_text = response['choices'][0]['message']['content']
            return response_text

        else:
            raise ValueError("Translation failed: no 'text' attribute in response")
        
    except Exception as e:
        print(f"Translation error: {str(e)}")
        return text  # Return original text on failure

from pyrogram import Client, filters

@Client.on_message(filters.command('settranslate', prefixes=['.', '!']) & filters.me)
async def set_translation_prefs(client, message):
    try:
        cmd = message.command
        if len(cmd) < 3:
            await message.edit(
                "❌ Usage: `.settranslate <model> <language>`\n"
                "Models: `googletrans`, `gemini`\n"
                "Languages: e.g., `fa` (Persian), `en` (English), `es` (Spanish)\n"
                "Example: `.settranslate gemini fa`"
            )
            return
        
        model = cmd[1].lower()
        language = cmd[2].lower()
        
        valid_models = ['googletrans', 'gemini', 'openrouter']
        if model not in valid_models:
            await message.edit(f"❌ Invalid model. Use one of: {', '.join(valid_models)}")
            return
        
        db.translation_prefs.update_one(
            {'user_id': message.from_user.id},
            {'$set': {'translate_model': model, 'target_lang': language}},
            upsert=True
        )
        
        await message.edit(f"✅ Translation preferences set: Model=`{model}`, Language=`{language}`")
    except Exception as e:
        await message.edit(f"⚠️ Error setting translation preferences: {str(e)}")