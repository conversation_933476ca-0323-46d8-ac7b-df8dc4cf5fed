from pyrogram import Client, filters
from helpers import db, gemini_cli, groq_cli, cloudflare_ai
from helpers.models import get_gemini_models, get_groq_models, get_cloudflare_models
from helpers.prompts import PROMPTS
from pudb import set_trace


@Client.on_message(filters.me & filters.command("help", prefixes=[".", "!"]))
async def help_command(client, message):
    cmd = message.command

    # If there's a subcommand, show specific help
    if len(cmd) > 1:
        subcommand = cmd[1].lower()
        if subcommand == "translate":
            help_text = (
                "**Translation Help**\n\n"
                "**Basic Usage:**\n"
                "`.translate <text>` - Translate using default settings\n"
                "`.translate -g/-t/-c/-o <text>` - Use specific provider\n"
                "`.translate <lang> <text>` - Translate to specific language\n"
                "`.translate <lang> -g <text>` - Combine language and provider\n\n"
                "**Available Models** (use with `-` flag):\n"
                "- `-g`: Gemini (AI-powered, context-aware)\n"
                "- `-t`: Google Translate (fast, basic)\n"
                "- `-c`: Cloudflare (M2M100 model)\n"
                "- `-o`: OpenRouter (various LLMs)\n\n"
                "**Supported Languages:**\n"
                "- `fa`: Persian (Farsi)\n"
                "- `en`: English\n"
                "- `ar`: Arabic\n"
                "- `tr`: Turkish\n"
                "- `de`: German\n"
                "- `es`: Spanish\n"
                "- `it`: Italian\n"
                "- `ja`: Japanese\n"
                "- `ko`: Korean\n"
                "- `zh`: Chinese\n"
                "- `hi`: Hindi\n"
                "- `ru`: Russian\n"
                "- `nl`: Dutch\n"
                "- `pt`: Portuguese\n"
                "- `fr`: French\n\n"
                "**Settings Command:**\n"
                "`.settranslate <model> <language>` - Set default preferences\n"
                "Valid models: `googletrans`, `gemini`, `openrouter`, `cloudflare`\n"
                "Example: `.settranslate gemini fa`\n\n"
                "**Language Detection:**\n"
                "The system automatically detects the source language and translates to your target language.\n"
                "If the text is already in your target language, it will be returned unchanged.\n\n"
                "**Examples:**\n"
                "1. Basic translation:\n"
                "   `.translate Hello, world!`\n"
                "2. Specify model:\n"
                "   `.translate -g مرحبا`\n"
                "3. Specify language:\n"
                "   `.translate es Hello friends`\n"
                "4. Combined:\n"
                "   `.translate fa -g How are you?`\n"
                "5. Reply to message:\n"
                "   `.translate -c` (in reply)\n"
                "6. Reply with language:\n"
                "   `.translate en` (in reply, translate to English)\n\n"
                "**Tips:**\n"
                "• Gemini is best for context-aware translations and preserving nuance\n"
                "• Google Translate is fastest for basic needs\n"
                "• Cloudflare works well for technical content\n"
                "• OpenRouter provides access to various LLMs for translation\n"
                "• Set defaults with `.settranslate` to save time\n"
                "• The system remembers your preferred language and model"
            )
            await message.edit(help_text)
            return
        elif subcommand == "genimg":
            help_text = (
                "**Image Generation Help**\n\n"
                "**Basic Usage:**\n"
                "`.genimg <prompt>` - Generate an image using default settings (Gemini model)\n"
                "`.genimg describe` - Analyze and describe a replied image\n\n"
                "**Available Models** (-m option):\n"
                "- `gemini`: Google's Gemini (default)\n"
                "  • Best for: Artistic and creative images\n"
                "  • Features: Text explanations included\n"
                "  • Resolution: 1024x1024\n"
                "  • Free tier available\n"
                "  • May fall back to text description if image generation is unavailable\n\n"
                "- `imagen`: Google's Imagen\n"
                "  • Best for: Photorealistic images\n"
                "  • Features: Multiple images (1-4)\n"
                "  • All aspect ratios supported\n"
                "  • Requires paid Gemini API account\n\n"
                "**Cloudflare Models:**\n"
                "- `dreamshaper`: Dreamshaper 8 LCM\n"
                "  • Best for: Photorealistic images\n"
                "  • Features: Fine-tuned for realism\n"
                "  • Resolution: 1024x1024\n\n"
                "- `flux`: FLUX.1 Schnell\n"
                "  • Best for: Quick generations\n"
                "  • Features: 12B parameter model\n"
                "  • Ultra-fast response time\n\n"
                "- `sdxl`: Stable Diffusion XL\n"
                "  • Best for: Premium quality\n"
                "  • Features: Advanced details\n"
                "  • Highest image fidelity\n\n"
                "- `sdxl-lightning`: SDXL Lightning\n"
                "  • Best for: Speed + Quality\n"
                "  • Features: Optimized pipeline\n"
                "  • Fastest SDXL variant\n\n"
                "**Image Editing Models:**\n"
                "- `img2img`: SD v1.5 Image-to-Image\n"
                "  • For modifying existing images\n"
                "  • Preserves original composition\n\n"
                "- `inpainting`: SD v1.5 Inpainting\n"
                "  • For selective image editing\n"
                "  • Precise area modifications\n\n"
                "**Arguments:**\n"
                "- `-n <number>`: Generate multiple images (1-4, Imagen only)\n"
                "- `-r <ratio>`: Set aspect ratio (1:1, 3:4, 4:3, 9:16, 16:9)\n"
                "- `-m <model>`: Choose the model\n\n"
                "**Image Editing:**\n"
                "`.editimg <prompt>` - Edit a replied image\n"
                "  • Works with Gemini or img2img models\n"
                "  • Must reply to an existing image\n\n"
                "**Example Prompts by Category:**\n\n"
                "🎨 **Artistic:**\n"
                "- `.genimg -m gemini watercolor painting of cherry blossoms`\n"
                "- `.genimg -m gemini vibrant abstract geometric art`\n\n"
                "👤 **Professional:**\n"
                "- `.genimg -m sdxl professional linkedin headshot`\n"
                "- `.genimg -m dreamshaper business team meeting`\n\n"
                "🌆 **Landscapes:**\n"
                "- `.genimg -m sdxl -r 16:9 epic mountain sunset`\n"
                "- `.genimg -m dreamshaper cyberpunk city night`\n\n"
                "✏️ **Editing Examples:**\n"
                "- `.editimg make background more professional`\n"
                "- `.editimg add dramatic lighting`\n"
                "- `.editimg change season to winter`\n\n"
                "💡 **Pro Tips:**\n"
                "• Add 'high quality, detailed' for better results\n"
                "• Specify lighting: 'dramatic', 'soft', 'studio'\n"
                "• Include style: 'photorealistic', 'cinematic'\n"
                "• Use SDXL for highest quality\n"
                "• Use FLUX/Lightning for speed\n"
                "• Use Gemini for artistic style\n"
                "• Use img2img for editing existing images"
            )
            await message.edit(help_text)
            return
        elif subcommand == "profile":
            help_text = (
                "**Profile Management Help**\n\n"
                "**Commands:**\n"
                "`.setprofile` or `.sp` - Update your Telegram profile\n\n"
                "**Provider Options:**\n"
                "- `.sp -gemini <prompt>` - Use Gemini for image generation\n"
                "- `.sp -cloudflare` or `.sp -cf <prompt>` - Use Cloudflare for image generation\n"
                "- `.sp <prompt>` - Auto-select best available provider\n\n"
                "**Features:**\n"
                "- Generate and set AI profile photos\n"
                "- Update username with AI suggestions\n"
                "- Change first name creatively\n"
                "- Complete profile makeover\n\n"
                "**Examples:**\n"
                "- `.sp generate a cool username for me`\n"
                "- `.sp -cf create a professional profile with photo`\n"
                "- `.sp -gemini make me look like a tech expert`\n"
                "- `.sp suggest a creative first name`\n"
                "- `.sp design a minimalist profile`\n\n"
                "**Tips:**\n"
                "- Be specific about the style you want\n"
                "- Mention if you want a photo update\n"
                "- If one provider fails, try the other one\n"
                "- Cloudflare often works better for realistic photos\n"
                "- Gemini may work better for creative styles\n"
                "- Usernames must be 5-32 characters\n"
                "- First names limited to 64 characters"
            )
            await message.edit(help_text)
            return
        elif subcommand == "tts":
            help_text = (
                "**Text-to-Speech Help**\n\n"
                "**Basic Usage:**\n"
                "`.tts <text>` - Convert text to speech using default provider (Cloudflare)\n"
                "`.tts <lang> <text>` - Specify language code\n"
                "`.tts -groq <text>` - Use Groq TTS instead of Cloudflare\n"
                "`.tts -groq voice=Fritz-PlayAI <text>` - Specify Groq voice\n\n"
                "**Providers:**\n"
                "- **Cloudflare** (default): Uses MeloTTS model\n"
                "  • Fast and efficient\n"
                "  • Good for most languages\n"
                "  • No voice selection\n\n"
                "- **Groq**: Uses PlayAI TTS models\n"
                "  • Higher quality voices\n"
                "  • 19 English voices\n"
                "  • 4 Arabic voices\n"
                "  • Specify with `-groq` flag\n\n"
                "- **Gemini**: Uses native Gemini TTS\n"
                "  • Premium quality voices\n"
                "  • 30 unique voices\n"
                "  • 24 supported languages\n"
                "  • Specify with `-gemini` flag\n\n"
                "**Groq English Voices:**\n"
                "Arista-PlayAI, Atlas-PlayAI, Basil-PlayAI, Briggs-PlayAI, \n"
                "Calum-PlayAI, Celeste-PlayAI, Cheyenne-PlayAI, Chip-PlayAI, \n"
                "Cillian-PlayAI, Deedee-PlayAI, Fritz-PlayAI, Gail-PlayAI, \n"
                "Indigo-PlayAI, Mamaw-PlayAI, Mason-PlayAI, Mikail-PlayAI, \n"
                "Mitch-PlayAI, Quinn-PlayAI, Thunder-PlayAI\n\n"
                "**Groq Arabic Voices:**\n"
                "Ahmad-PlayAI, Amira-PlayAI, Khalid-PlayAI, Nasser-PlayAI\n\n"
                "**Gemini Voices (selection):**\n"
                "Kore (Firm), Puck (Upbeat), Charon (Informative), Zephyr (Bright), \n"
                "Fenrir (Excitable), Leda (Youthful), Aoede (Breezy), Enceladus (Breathy), \n"
                "Achernar (Soft), Sulafat (Warm), and 20 more voices\n\n"
                "**Supported Languages:**\n"
                "en, fr, es, de, it, pt, pl, tr, ru, nl, cs, ar, zh, ja, ko\n\n"
                "**Examples:**\n"
                "1. Basic TTS with default provider:\n"
                "   `.tts Hello, how are you today?`\n"
                "2. Specify language:\n"
                "   `.tts fr Bonjour, comment allez-vous?`\n"
                "3. Use Groq with default voice:\n"
                "   `.tts -groq This is a test of Groq TTS`\n"
                "4. Specify Groq voice:\n"
                "   `.tts -groq voice=Celeste-PlayAI I'm speaking with Celeste's voice`\n"
                "5. Arabic with Groq:\n"
                "   `.tts -groq ar voice=Ahmad-PlayAI مرحبا بالعالم`\n"
                "6. Use Gemini with default voice:\n"
                "   `.tts -gemini This is premium quality speech`\n"
                "7. Specify Gemini voice:\n"
                "   `.tts -gemini voice=Puck I'm speaking with an upbeat voice`\n"
                "8. Reply to a message:\n"
                "   `.tts -gemini voice=Sulafat` (in reply to text)\n"
                "9. Style control (reply):\n"
                "   `.tts -gemini read this cheerfully` (controls tone/style)\n"
                "   `.tts -gemini say in a spooky whisper` (dramatic effect)\n"
                "10. Inline style control:\n"
                "   `.tts -gemini Say cheerfully: Hello world!`\n"
                "   `.tts -gemini Read dramatically: Welcome everyone`\n\n"
                "**Tips:**\n"
                "• Try different voices to find your favorite\n"
                "• Gemini offers the highest quality with 30 unique voices\n"
                "• Use style prompts with Gemini for tone control (reply mode only)\n"
                "• Groq works best for English and Arabic\n"
                "• For other languages, Cloudflare may work better\n"
                "• Specify language code before the text\n"
                "• Voice selection comes after provider flag"
            )
            await message.edit(help_text)
            return
        elif subcommand == "models":
            help_text = (
                "**Models Command Help**\n\n"
                "**Basic Usage:**\n"
                "`.models` - List all available models from the database\n"
                "`.models g` - Fetch latest Gemini models with descriptions\n"
                "`.models q` - Fetch latest Groq models directly from API\n\n"
                "**Provider Information:**\n"
                "- **Groq Models** (`q`):\n"
                "  • High-performance LLMs like Llama 3, Qwen, and Gemma\n"
                "  • Optimized for chat and completion tasks\n"
                "  • Includes specialized models for different use cases\n"
                "  • Examples: llama-3.3-70b-versatile, qwen-2.5-32b\n\n"
                "- **Gemini Models** (`g`):\n"
                "  • Google's multimodal AI models\n"
                "  • Support for text, images, and code\n"
                "  • Various sizes for different performance needs\n"
                "  • Examples: gemini-1.5-pro, gemini-2.0-flash\n\n"
                "- **Cloudflare Models**:\n"
                "  • Wide variety of hosted models\n"
                "  • Includes text, image, and TTS models\n"
                "  • Organized by provider (Meta, Mistral, Google, etc.)\n"
                "  • Examples: @cf/meta/llama-3.1-70b-instruct\n\n"
                "**Setting Default Models:**\n"
                "Use `.setmodel <provider> <model>` to set a default model\n"
                "Example: `.setmodel groq llama-3.3-70b-versatile`\n\n"
                "**Examples:**\n"
                "1. List all models from database:\n"
                "   `.models`\n"
                "2. Fetch latest Gemini models with descriptions:\n"
                "   `.models g`\n"
                "3. Fetch latest Groq models from API:\n"
                "   `.models q`\n"
                "4. Set a default model after viewing options:\n"
                "   `.setmodel google gemini-1.5-pro`\n\n"
                "**Tips:**\n"
                "• Use `g` and `q` flags to get the most up-to-date model information\n"
                "• Models fetched with flags come directly from the provider APIs\n"
                "• The default command shows models stored in the database\n"
                "• Different models excel at different tasks - experiment to find the best fit\n"
                "• Larger models (higher parameter count) are generally more capable but slower"
            )
            await message.edit(help_text)
            return

    # Original general help menu
    help_text = (
        "**Selfbot Help Menu**\n\n"
        "**Commands:**\n"
        "- `.ask [-q|-g|-o|-r|-c] <mode> [getreplies] [getmsg:N] [context]`: Ask the AI a question.\n"
        "  - `-q`: Groq, `-g`: Google, `-o`: Ollama, `-r`: OpenRouter, `-c`: Cloudflare\n"
        "  - `getreplies`: Include the full reply chain for context\n"
        "  - `getmsg:N`: Get N previous messages from the user you're replying to (e.g., getmsg:10)\n"
        "  - When replying to a photo and using `-g` (Google), the AI will analyze the image\n"
        "- `.chat <message>`: Start or continue a chat with Gemini AI\n"
        "  - `.chat clear`: Clear chat history\n"
        "  - `.chat history`: Show chat history\n"
        "- `.setmodel <provider> <model>`: Set the default model for a provider.\n"
        "- `.models [g|q]`: List available models.\n          - No args: List all models from database\n          - `g`: Fetch latest Gemini models with descriptions\n          - `q`: Fetch latest Groq models directly from API\n"
        "- `.prompts`: List all available prompt modes.\n"
        "- `.docprocess <action>`: Process a document (reply to a file or URL).\n"
        "  - Actions: `upload`, `summarize`, `extract`, `question <query>`, `transcribe`, `ask <query>`\n"
        "- `.ffmpeg <instructions>`: Process a replied media file with AI-generated FFmpeg commands.\n"
        "- `.settranslate <model> <language>`: Set preferred translation model and language.\n"
        "  - Models: `googletrans`, `gemini`, `openrouter`, `cloudflare`; Languages: e.g., `fa`, `en`\n"
        "- `.translate <text/reply>`: Translate text using preferred settings.\n"
        "- `.genimg <prompt>`: Generate AI images with optional parameters (-n, -r, -m).\n"
        "- `.genimg describe`: Analyze and describe a replied image.\n"
        "- `.editimg <prompt>`: Edit a replied image using AI.\n"
        "- `.setprofile` or `.sp [-gemini|-cf] <prompt>`: Update profile with AI (photo, username, name).\n"
        "- `.tts [-groq|-cf|-gemini] [voice=VoiceName] [<lang>] <text>`: Convert text to speech.\n"
        "  - Providers: Cloudflare (MeloTTS, default), Groq (PlayAI TTS), Gemini (Premium TTS)\n"
        "  - Supported languages: en, fr, es, de, it, pt, pl, tr, ru, nl, cs, ar, zh, ja, ko\n"
        "  - For Groq voices, use voice=VoiceName (e.g., voice=Fritz-PlayAI)\n"
        "  - For Gemini voices, use voice=VoiceName (e.g., voice=Puck)\n"
        "- `.help`: Show this menu.\n"
        "- `.help genimg`: Show detailed image generation help.\n"
        "- `.help profile`: Show profile management help.\n"
        "- `.help translate`: Show translation help.\n"
        "- `.help tts`: Show text-to-speech help.\n"
        "- `.help models`: Show models command help.\n\n"
        "**Modes (Prompts):**\n"
        "- `code_expert`: Code assistance\n"
        "- `deep_analyze`: Deep analysis with a serious perspective\n"
        "- `fun`: Lighthearted and humorous response\n"
        "- *(use `.prompts` for full list)*\n\n"
        "**Examples:**\n"
        "- `.ask -q code_expert getreplies`: Analyze replies with Groq\n"
        "- `.ask -q helpful getmsg:10`: Analyze 10 previous messages from the user\n"
        "- `.ask -g helpful`: Reply to a photo to analyze it with Google/Gemini\n"
        "- `.setmodel groq llama-3.3-70b-versatile`: Set Groq's default model\n"
        "- `.models g`: Fetch latest Gemini models from API\n"
        "- `.prompts`: See all prompts\n"
        "- `.docprocess upload`: Upload a document to Gemini File API\n"
        "- `.docprocess summarize`: Summarize a replied document\n"
        "- `.docprocess ask What is the main theme?`: Ask about a previously uploaded document\n"
        "- `.ffmpeg speed up by 2x`: Speed up the replied media file by 2x\n"
        "- `.settranslate gemini fa`: Set translation to Persian with Gemini\n"
        "- `.sp -cf create a professional profile`: Update profile with Cloudflare AI\n"
        "- `.cleanmychat`: Delete all your messages in the chat\n"
        "- `.help`: Show this menu"
    )
    await message.edit(help_text)

@Client.on_message(filters.me & filters.command('prompts', prefixes=['.', '!']))
async def list_prompts(_, message):
    try:
        prompts_text = "**Available Prompts (Modes):**\n\n"
        for prompt_name, prompt_desc in PROMPTS.items():
            short_desc = prompt_desc.split('.')[0] if '.' in prompt_desc else prompt_desc
            prompts_text += f"- `{prompt_name}`: {short_desc}\n"

        chunks = [prompts_text[i:i+4000] for i in range(0, len(prompts_text), 4000)]
        for i, chunk in enumerate(chunks):
            if i == 0:
                await message.edit(chunk)
            else:
                await message.reply(chunk)
    except Exception as e:
        await message.edit(f"⚠️ Error fetching prompts: {str(e)}")

@Client.on_message(filters.me & filters.command("models", prefixes=[".", "!"])) 
async def list_models(client, message):
    try:
        cmd = message.command
        # Check if there are any arguments
        if len(cmd) > 1:
            arg = cmd[1].lower()
            # Fetch Gemini models dynamically
            if arg == 'g':
                from helpers import gemini_cli, get_gemini_models
                await message.edit("🔍 Fetching latest Gemini models from API...")
                if gemini_cli is None:
                    await message.edit("⚠️ Google API client is not initialized. Check your API key.")
                    return
                try:
                    # Get models with descriptions
                    models = get_gemini_models(gemini_cli, include_descriptions=True)
                    # Get the default model from the database
                    provider = db.providers.find_one({'_id': 'google'})
                    default_model = provider['default_model'] if provider and 'default_model' in provider else 'gemini-1.5-pro'
                    model_list = f"**Gemini Models** (Default: `{default_model}`):\n\n"
                    # Format each model with its description
                    for model in models:
                        model_name = model['name']
                        display_name = model['display_name']
                        description = model['description']
                        # Add model name (bold if it's the default)
                        if model_name == default_model:
                            model_list += f"**`{model_name}`**"
                        else:
                            model_list += f"`{model_name}`"
                        # Add display name if available
                        if display_name:
                            model_list += f" - {display_name}"
                        # Add description if available (truncated if too long)
                        if description:
                            # Truncate description if it's too long
                            if len(description) > 100:
                                description = description[:97] + "..."
                            model_list += f"\n   {description}"
                        model_list += "\n\n"
                    # Split into chunks if needed
                    chunks = [model_list[i:i+4000] for i in range(0, len(model_list), 4000)]
                    for i, chunk in enumerate(chunks):
                        if i == 0:
                            await message.edit(chunk)
                        else:
                            await message.reply(chunk)
                    return
                except Exception as e:
                    await message.edit(f"⚠️ Error fetching Gemini models from API: {str(e)}")
                    return
            # Fetch Groq models dynamically
            elif arg == 'q':
                from helpers import groq_cli, get_groq_models
                await message.edit("🔍 Fetching latest Groq models from API...")
                if groq_cli is None:
                    await message.edit("⚠️ Groq API client is not initialized. Check your API key.")
                    return
                try:
                    models = get_groq_models(groq_cli)
                    model_list = "**Groq Models** (Fetched directly from API):\n"
                    model_list += "\n".join([f"- `{model}`" for model in models])
                    # Get the default model from the database
                    provider = db.providers.find_one({'_id': 'groq'})
                    if provider and 'default_model' in provider:
                        model_list = f"**Groq Models** (Default: `{provider['default_model']}`):\n" + model_list.split(':\n')[1]
                    await message.edit(model_list)
                    return
                except Exception as e:
                    await message.edit(f"⚠️ Error fetching Groq models from API: {str(e)}")
                    return
            else:
                await message.edit(f"⚠️ Unknown argument: {arg}\n\nUsage:\n- `.models` - List all models\n- `.models g` - Fetch latest Gemini models\n- `.models q` - Fetch latest Groq models")
                return
        # Default behavior: list all models from the database
        await message.edit("🔍 Fetching available models from all providers...")
        model_list = ""
        # Groq
        if groq_cli:
            try:
                models = get_groq_models(groq_cli)
                provider = db.providers.find_one({'_id': 'groq'})
                default_model = provider['default_model'] if provider and 'default_model' in provider else ''
                model_list += f"**Groq Models** (Default: `{default_model}`):\n"
                model_list += "\n".join([f"- `{model}`" for model in models]) + "\n\n"
            except Exception as e:
                model_list += f"**Groq Models**: ⚠️ Error fetching models: {str(e)}\n\n"
        # Google
        if gemini_cli:
            try:
                models = get_gemini_models(gemini_cli)
                provider = db.providers.find_one({'_id': 'google'})
                default_model = provider['default_model'] if provider and 'default_model' in provider else ''
                model_list += f"**Google Models** (Default: `{default_model}`):\n"
                model_list += "\n".join([f"- `{model}`" for model in models]) + "\n\n"
            except Exception as e:
                model_list += f"**Google Models**: ⚠️ Error fetching models: {str(e)}\n\n"
        # Cloudflare
        if cloudflare_ai:
            try:
                models = get_cloudflare_models(cloudflare_ai)
                provider = db.providers.find_one({'_id': 'cloudflare'})
                default_model = provider['default_model'] if provider and 'default_model' in provider else ''
                model_list += f"**Cloudflare Models** (Default: `{default_model}`):\n"
                model_list += "\n".join([f"- `{model}`" for model in models]) + "\n\n"
            except Exception as e:
                model_list += f"**Cloudflare Models**: ⚠️ Error fetching models: {str(e)}\n\n"
        if not model_list:
            await message.edit("No models found.")
            return
        chunks = [model_list[i:i+4000] for i in range(0, len(model_list), 4000)]
        for i, chunk in enumerate(chunks):
            if i == 0:
                await message.edit(chunk)
            else:
                await message.reply(chunk)
    except Exception as e:
        await message.edit(f"⚠️ Error fetching models: {str(e)}")
